{"wallet": {"address": "addr_test1vzpwq95z3xyum8vqndgdd9mdnmafh3djcxnc6jemlgdmswcve6tkw", "mnemonic": null, "purpose": "Large amount testing with working faucet address"}, "balance": 118466133.366449, "tests": [{"success": true, "cbor": "84a400d9010281825820ba5cc479647e7dd872901e89347c0dc626e1b073e513b50e1e6e2e92f57fad491821018282581d706eb7c29d9362dd710902977bb645eb97bb76c6246768851e76489f1a0a82581d6082e016828989cd9d809b50d6976d9efa9bc5b2c1a78d4b3bfa1bb83b1a007a11f6021a001e8480031a05cd7178a0f5f6", "amount": 10, "type": "deposit"}, {"success": false, "error": "{\"success\":false,\"error\":\"Insufficient vault balance. Available: 2 ADA, Requested: 10 ADA\"}", "amount": 10, "type": "withdrawal"}, {"success": true, "cbor": "84a400d9010281825820ba5cc479647e7dd872901e89347c0dc626e1b073e513b50e1e6e2e92f57fad491821018282581d706eb7c29d9362dd710902977bb645eb97bb76c6246768851e76489f1a186482581d6082e016828989cd9d809b50d6976d9efa9bc5b2c1a78d4b3bfa1bb83b1a007a119c021a001e8480031a05cd7178a0f5f6", "amount": 100, "type": "deposit"}, {"success": false, "error": "{\"success\":false,\"error\":\"Insufficient vault balance. Available: 2 ADA, Requested: 100 ADA\"}", "amount": 100, "type": "withdrawal"}, {"success": true, "cbor": "84a400d9010281825820ba5cc479647e7dd872901e89347c0dc626e1b073e513b50e1e6e2e92f57fad491821018282581d706eb7c29d9362dd710902977bb645eb97bb76c6246768851e76489f1a1901f482581d6082e016828989cd9d809b50d6976d9efa9bc5b2c1a78d4b3bfa1bb83b1a007a100c021a001e8480031a05cd7178a0f5f6", "amount": 500, "type": "deposit"}, {"success": false, "error": "{\"success\":false,\"error\":\"Insufficient vault balance. Available: 2 ADA, Requested: 500 ADA\"}", "amount": 500, "type": "withdrawal"}, {"success": true, "cbor": "84a400d9010281825820ba5cc479647e7dd872901e89347c0dc626e1b073e513b50e1e6e2e92f57fad491821018282581d706eb7c29d9362dd710902977bb645eb97bb76c6246768851e76489f1a1903e882581d6082e016828989cd9d809b50d6976d9efa9bc5b2c1a78d4b3bfa1bb83b1a007a0e18021a001e8480031a05cd7178a0f5f6", "amount": 1000, "type": "deposit"}, {"success": false, "error": "{\"success\":false,\"error\":\"Insufficient vault balance. Available: 2 ADA, Requested: 1000 ADA\"}", "amount": 1000, "type": "withdrawal"}], "summary": {"total": 8, "successful": 4, "failed": 4}}