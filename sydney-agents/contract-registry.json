[{"contractAddress": "addr1wxwx5rmqrwm4mpeg5ky6rt6lq76errkjjs490pewl9rqvrcqzrec7", "scriptHash": "9c6a0f601bb75d8728a589a1af5f07b5918ed2942a57872ef946060f", "scriptCBOR": "unknown", "plutusVersion": "V3", "status": "stuck", "purpose": "agent_vault_v1", "balance": 10, "metadata": {"notes": "Contract stuck due to script hash mismatch - 10 ADA locked"}, "id": "contract_1752955562381_cwxingeb3", "deployedAt": "2025-07-19T20:06:02.381Z"}, {"contractAddress": "addr1wyq32c96u0u04s54clgeqtjk6ffd56pcxnrmu4jzn57zj3sy9gwyk", "scriptHash": "011560bae3f8fac295c7d1902e56d252da683834c7be56429d3c2946", "scriptCBOR": "unknown", "plutusVersion": "V3", "status": "stuck", "purpose": "agent_vault_v2", "balance": 10, "metadata": {"notes": "Contract stuck due to script hash mismatch - 10 ADA locked"}, "id": "contract_1752955562383_n7a4hh0dy", "deployedAt": "2025-07-19T20:06:02.383Z"}, {"contractAddress": "addr1w8gnkw8z0jlyk4zsrc6rp5nv5wa9nxqmceq50jdarf0c9gs2vc87j", "scriptHash": "d13b38e27cbe4b54501e3430d26ca3ba59981bc64147c9bd1a5f82a2", "scriptCBOR": "5857010100323232323225333002323232323253330073370e900118041baa00113233224a260160026016601800260126ea800458c024c02800cc020008c01c008c01c004c010dd50008a4c26cacae6955ceaab9e5742ae89", "plutusVersion": "V2", "status": "deprecated", "purpose": "emergency_agent_vault", "metadata": {"aikenSourceFile": "emergency_agent_vault.ak", "compilationTimestamp": "2025-07-19T20:06:02.387Z", "deploymentMethod": "enhanced_automated_pipeline", "notes": "Test failed: undefined"}, "id": "contract_1752955562387_7xdxbaqvf", "deployedAt": "2025-07-19T20:06:02.387Z"}]