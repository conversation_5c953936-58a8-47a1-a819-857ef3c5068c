import { NextApiRequest, NextApiResponse } from 'next';

const TAPTOOLS_API_KEY = 'WghkJaZlDWYdQFsyt3uiLdTIOYnR5uhO';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { address } = req.query;
    
    if (!address || typeof address !== 'string') {
      return res.status(400).json({ error: 'Valid address is required' });
    }

    // For testnet addresses, return empty handles since Taptools is mainnet only
    if (address.startsWith('addr_test')) {
      return res.status(200).json({
        address,
        handles: [],
        network: 'testnet'
      });
    }

    // Try to fetch handles from Taptools API
    try {
      const response = await fetch(`https://api.taptools.io/api/v1/addresses/${address}/handles`, {
        headers: {
          'X-API-Key': TAPTOOLS_API_KEY
        }
      });

      if (!response.ok) {
        throw new Error(`Taptools API error: ${response.status}`);
      }

      const data = await response.json();
      
      return res.status(200).json({
        address,
        handles: data.handles || [],
        network: 'mainnet'
      });

    } catch (apiError) {
      // If Taptools fails, return empty handles
      console.warn('Taptools API failed, returning empty handles:', apiError);
      return res.status(200).json({
        address,
        handles: [],
        network: 'mainnet',
        warning: 'Handle lookup failed'
      });
    }

  } catch (error) {
    console.error('Handles check error:', error);
    return res.status(500).json({ 
      error: 'Failed to fetch handles',
      details: error instanceof Error ? error.message : String(error)
    });
  }
}
