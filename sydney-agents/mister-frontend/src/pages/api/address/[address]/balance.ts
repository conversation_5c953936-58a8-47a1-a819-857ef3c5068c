import { NextApiRequest, NextApiResponse } from 'next';

const BLOCKFROST_API_KEY = process.env.BLOCKFROST_API_KEY || 'mainnetKDR7gGfvHy85Mqr4nYtfjoXq7fX8R1Bu';
const BLOCKFROST_TESTNET_API_KEY = process.env.BLOCKFROST_TESTNET_API_KEY || 'preprodfHBBQsTsk1g3Lna67Vqb8HqZ0NbcPo1f';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { address } = req.query;
    
    if (!address || typeof address !== 'string') {
      return res.status(400).json({ error: 'Valid address is required' });
    }

    // Determine if this is a testnet address
    const isTestnet = address.startsWith('addr_test');
    const apiKey = isTestnet ? BLOCKFROST_TESTNET_API_KEY : BLOCKFROST_API_KEY;
    const baseUrl = isTestnet 
      ? 'https://cardano-preprod.blockfrost.io/api/v0'
      : 'https://cardano-mainnet.blockfrost.io/api/v0';

    const response = await fetch(`${baseUrl}/addresses/${address}`, {
      headers: {
        'project_id': apiKey
      }
    });

    if (!response.ok) {
      if (response.status === 404) {
        return res.status(200).json({
          address,
          balance: '0',
          balanceAda: 0,
          utxos: [],
          network: isTestnet ? 'testnet' : 'mainnet'
        });
      }
      throw new Error(`Blockfrost API error: ${response.status}`);
    }

    const data = await response.json();
    const lovelaceAmount = data.amount?.find((a: any) => a.unit === 'lovelace')?.quantity || '0';
    const adaAmount = parseInt(lovelaceAmount) / 1000000;

    return res.status(200).json({
      address,
      balance: lovelaceAmount,
      balanceAda: adaAmount,
      utxos: data.amount || [],
      network: isTestnet ? 'testnet' : 'mainnet'
    });

  } catch (error) {
    console.error('Balance check error:', error);
    return res.status(500).json({ 
      error: 'Failed to fetch balance',
      details: error instanceof Error ? error.message : String(error)
    });
  }
}
