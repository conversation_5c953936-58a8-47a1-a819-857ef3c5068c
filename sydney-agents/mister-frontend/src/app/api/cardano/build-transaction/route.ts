import { NextRequest, NextResponse } from 'next/server';

interface TransactionRequest {
  fromAddress: string;
  toAddress: string;
  amount: number; // in ADA
  vaultDatum?: any;
  network?: 'mainnet' | 'testnet';
}

export async function POST(request: NextRequest) {
  try {
    const body: TransactionRequest = await request.json();
    const { fromAddress, toAddress, amount, vaultDatum, network = 'testnet' } = body;

    console.log(`🔨 Building Cardano transaction via Blockfrost (${network.toUpperCase()})...`);
    console.log(`💰 From: ${fromAddress.substring(0, 20)}...`);
    console.log(`💰 To: ${toAddress.substring(0, 20)}...`);
    console.log(`💰 Amount: ${amount} ${network === 'testnet' ? 'tADA' : 'ADA'}`);
    console.log(`🌐 Network: ${network.toUpperCase()}`);

    // Convert amount from ADA to lovelace
    const amountLovelace = Math.round(amount * 1000000);

    // 🧪 TESTNET SUPPORT: Configure Blockfrost for network
    const blockfrostConfig = network === 'testnet'
      ? {
          projectId: process.env.BLOCKFROST_TESTNET_PROJECT_ID || 'preprodfHBBQsTsk1g3Lna67Vqb8HqZ0NbcPo1f',
          baseUrl: 'https://cardano-preprod.blockfrost.io/api/v0'
        }
      : {
          projectId: process.env.BLOCKFROST_PROJECT_ID || 'mainnetKDR7gGfvHy85Mqr4nYtfjoXq7fX8R1Bu',
          baseUrl: 'https://cardano-mainnet.blockfrost.io/api/v0'
        };

    console.log(`🔗 Using Blockfrost: ${blockfrostConfig.baseUrl}`);
    const blockfrostProjectId = blockfrostConfig.projectId;
    const blockfrostBaseUrl = blockfrostConfig.baseUrl;
    
    // Step 1: Get UTxOs from the sender address
    console.log('🔍 DEBUG: About to fetch UTxOs with:');
    console.log(`  URL: ${blockfrostBaseUrl}/addresses/${fromAddress}/utxos`);
    console.log(`  Project ID: ${blockfrostProjectId}`);
    console.log(`  Address: ${fromAddress}`);

    const utxosResponse = await fetch(`${blockfrostBaseUrl}/addresses/${fromAddress}/utxos`, {
      headers: {
        'project_id': blockfrostProjectId
      }
    });

    if (!utxosResponse.ok) {
      const errorText = await utxosResponse.text();
      console.error(`❌ Blockfrost UTxO fetch failed:`, {
        status: utxosResponse.status,
        statusText: utxosResponse.statusText,
        address: fromAddress,
        errorBody: errorText
      });
      throw new Error(`Failed to fetch UTxOs: ${utxosResponse.statusText} - ${errorText}`);
    }

    const utxos = await utxosResponse.json();

    if (!utxos || utxos.length === 0) {
      throw new Error('No UTxOs found at sender address');
    }

    // Step 2: Get protocol parameters
    const protocolResponse = await fetch(`${blockfrostBaseUrl}/epochs/latest/parameters`, {
      headers: {
        'project_id': blockfrostProjectId
      }
    });

    if (!protocolResponse.ok) {
      throw new Error(`Failed to fetch protocol parameters: ${protocolResponse.statusText}`);
    }

    const protocolParams = await protocolResponse.json();

    // Step 3: Build REAL CBOR transaction using CSL
    console.log('🔧 Building REAL CBOR transaction using CSL...');

    const CSL = await import('@emurgo/cardano-serialization-lib-nodejs');

    // Filter UTxOs to ONLY include pure ADA (no native tokens/NFTs)
    console.log('🔍 Filtering UTxOs for ADA-only...');
    const adaOnlyUtxos = utxos.filter((utxo: any) => {
      return utxo.amount.length === 1 && utxo.amount[0].unit === 'lovelace';
    });

    console.log(`📊 Found ${adaOnlyUtxos.length} ADA-only UTxOs out of ${utxos.length} total`);

    if (adaOnlyUtxos.length === 0) {
      throw new Error('No ADA-only UTxOs available for transaction');
    }

    // Calculate required amount (amount + estimated fee)
    const estimatedFee = 2000000; // 2 ADA estimated fee
    const requiredAmount = amountLovelace + estimatedFee;
    console.log(`💰 Need ${requiredAmount} lovelace (${amount} ADA + ${estimatedFee / 1000000} ADA fee)`);

    // Select UTxO with enough balance
    let selectedUtxo = null;
    for (const utxo of adaOnlyUtxos) {
      const utxoAmount = parseInt(utxo.amount[0].quantity);
      if (utxoAmount >= requiredAmount) {
        selectedUtxo = utxo;
        console.log(`📥 Selected UTxO: ${utxoAmount} lovelace`);
        break;
      }
    }

    if (!selectedUtxo) {
      throw new Error(`Insufficient funds. Need ${requiredAmount} lovelace, but no single UTxO has enough.`);
    }

    // Calculate change
    const inputAmount = parseInt(selectedUtxo.amount[0].quantity);
    const changeAmount = inputAmount - requiredAmount;
    console.log(`💸 Change amount: ${changeAmount} lovelace`);

    // Build transaction
    const txBuilder = CSL.TransactionBuilder.new(
      CSL.TransactionBuilderConfigBuilder.new()
        .fee_algo(CSL.LinearFee.new(
          CSL.BigNum.from_str(protocolParams.min_fee_a.toString()),
          CSL.BigNum.from_str(protocolParams.min_fee_b.toString())
        ))
        .pool_deposit(CSL.BigNum.from_str(protocolParams.pool_deposit))
        .key_deposit(CSL.BigNum.from_str(protocolParams.key_deposit))
        .coins_per_utxo_word(CSL.BigNum.from_str(protocolParams.coins_per_utxo_size))
        .max_value_size(protocolParams.max_val_size)
        .max_tx_size(protocolParams.max_tx_size)
        .build()
    );

    // Add input
    const txInput = CSL.TransactionInput.new(
      CSL.TransactionHash.from_bytes(Buffer.from(selectedUtxo.tx_hash, 'hex')),
      selectedUtxo.output_index
    );

    const inputValue = CSL.Value.new(CSL.BigNum.from_str(selectedUtxo.amount[0].quantity));
    txBuilder.add_input(
      CSL.Address.from_bech32(fromAddress),
      txInput,
      inputValue
    );

    // Add output to contract
    console.log(`🔍 FULL Contract address to parse: "${toAddress}"`);
    console.log(`🔍 Contract address length: ${toAddress.length} characters`);
    console.log(`🔍 Expected length: 63 characters for mainnet address`);
    console.log(`🔍 First 20 chars: "${toAddress.substring(0, 20)}"`);
    console.log(`🔍 Last 20 chars: "${toAddress.substring(toAddress.length - 20)}"`);

    const contractAddr = CSL.Address.from_bech32(toAddress);
    console.log('✅ Successfully parsed contract address');

    const outputValue = CSL.Value.new(CSL.BigNum.from_str(amountLovelace.toString()));
    
    let txOutput;
    if (vaultDatum) {
      // Add datum for smart contract
      const plutusData = CSL.PlutusData.from_json(JSON.stringify(vaultDatum), CSL.PlutusDatumSchema.DetailedSchema);
      txOutput = CSL.TransactionOutput.new(contractAddr, outputValue);
      txOutput.set_datum(CSL.Datum.new_data(plutusData));
    } else {
      txOutput = CSL.TransactionOutput.new(contractAddr, outputValue);
    }

    txBuilder.add_output(txOutput);

    // Add change output if needed
    if (changeAmount > 0) {
      const changeAddr = CSL.Address.from_bech32(fromAddress);
      const changeValue = CSL.Value.new(CSL.BigNum.from_str(changeAmount.toString()));
      const changeOutput = CSL.TransactionOutput.new(changeAddr, changeValue);
      txBuilder.add_output(changeOutput);
      console.log(`🔄 Added change output: ${changeAmount} lovelace to ${fromAddress}`);
    }

    // Get current slot for TTL
    const latestBlockResponse = await fetch(`${blockfrostBaseUrl}/blocks/latest`, {
      headers: { 'project_id': blockfrostProjectId }
    });

    if (!latestBlockResponse.ok) {
      throw new Error('Failed to fetch latest block for TTL calculation');
    }

    const latestBlock = await latestBlockResponse.json();
    const currentSlot = latestBlock.slot;
    const ttlSlot = currentSlot + 7200; // 2 hours from now

    console.log(`🕐 Current slot: ${currentSlot}`);
    console.log(`🕐 TTL slot: ${ttlSlot}`);

    txBuilder.set_ttl(ttlSlot);

    // Build the transaction
    const txBody = txBuilder.build();
    const tx = CSL.Transaction.new(
      txBody,
      CSL.TransactionWitnessSet.new()
    );

    // Convert to CBOR hex
    const cborHex = Buffer.from(tx.to_bytes()).toString('hex');

    console.log('✅ REAL CBOR transaction built successfully using CSL!');
    console.log('📋 CBOR length:', cborHex.length, 'characters');
    console.log('🔥 This is ACTUAL CBOR that Vespr can sign, not fake JSON!');

    return NextResponse.json({
      success: true,
      cborHex: cborHex
    });

  } catch (error) {
    console.error('❌ Transaction building failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
