'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Shield, CheckCircle, TestTube } from "lucide-react";

interface ConnectedWalletInfo {
  address: string;
  stakeAddress: string;
  walletType: string;
  balance: number;
  handle: string | null;
  displayName: string;
}

interface AgentVaultCreationProps {
  connectedWallet: ConnectedWalletInfo;
  onVaultCreated: (vaultInfo: any) => void;
  onError: (error: string) => void;
}

// 🧪 TESTNET CONFIGURATION - SAFE TESTING ENVIRONMENT
const USE_TESTNET = true;

const VAULT_CONFIG = {
  contractAddress: USE_TESTNET
    ? "addr_test1wpht0s5ajd3d6ugfq2thhdj9awtmkakxy3nk3pg7weyf7xs6nm2gz" // Testnet contract
    : "addr1wxwx5rmqrwm4mpeg5ky6rt6lq76errkjjs490pewl9rqvrcqzrec7", // Mainnet contract
  network: USE_TESTNET ? "testnet" : "mainnet"
};

export function AgentVaultCreationSimple({
  connectedWallet,
  onVaultCreated,
  onError
}: AgentVaultCreationProps) {
  const [isCreating, setIsCreating] = useState(false);
  const [depositAmount, setDepositAmount] = useState('1');
  const [maxTradeAmount, setMaxTradeAmount] = useState('1');

  const handleCreateVault = async () => {
    setIsCreating(true);

    try {
      console.log('🚀 DIRECT WALLET TEST - Creating Agent Vault...');
      console.log('💰 Deposit Amount:', depositAmount, 'ADA');
      console.log('📍 Contract:', VAULT_CONFIG.contractAddress);
      console.log('🌐 Network:', VAULT_CONFIG.network);

      // Get wallet API directly (bypass context)
      if (!window.cardano || !window.cardano.vespr) {
        throw new Error('Vespr wallet not available');
      }

      console.log('🔍 Connecting to Vespr wallet directly...');
      const walletApi = await window.cardano.vespr.enable();

      // Check network ID
      console.log('🌐 Checking wallet network...');
      const networkId = await (walletApi as any).getNetworkId();
      console.log('🔍 Network ID:', networkId, '(0=testnet, 1=mainnet)');

      if (networkId !== 0) {
        throw new Error(`Wrong network! Please switch Vespr to Preprod testnet. Current network ID: ${networkId}`);
      }

      // Get wallet balance
      console.log('💰 Checking wallet balance...');
      const balance = await (walletApi as any).getBalance();
      console.log('🔍 Raw balance:', balance);

      // Get REAL addresses from wallet (not cached)
      console.log('🔍 Getting addresses from wallet...');
      const addresses = await (walletApi as any).getUsedAddresses();
      const rewardAddresses = await (walletApi as any).getRewardAddresses();

      console.log('🔍 Raw addresses from wallet:');
      console.log('  Used addresses:', addresses);
      console.log('  Reward addresses:', rewardAddresses);

      if (!addresses || addresses.length === 0) {
        throw new Error('No addresses found in wallet');
      }

      const realPaymentAddress = addresses[0];
      const realStakeAddress = rewardAddresses?.[0];

      console.log('🔍 Parsed addresses:');
      console.log('  Payment:', realPaymentAddress);
      console.log('  Stake:', realStakeAddress);

      // Create vault datum
      const vaultDatum = {
        constructor: 0,
        fields: [
          { bytes: "34f1cf4a537fed73e8061ff16ec347c8cf62be45abc89ec1493aba3d" }, // User VKH
          { constructor: 1, fields: [] }, // Trading enabled
          { int: (parseFloat(maxTradeAmount) * 1000000).toString() }, // Max trade amount in lovelace
          { int: "10" } // Leverage
        ]
      };

      // Build transaction with REAL address
      console.log('🔨 Building transaction with real address...');
      const response = await fetch('/api/cardano/build-transaction', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fromAddress: realPaymentAddress, // Use REAL address from wallet
          toAddress: VAULT_CONFIG.contractAddress,
          amount: parseFloat(depositAmount),
          vaultDatum: vaultDatum,
          network: VAULT_CONFIG.network
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Transaction building failed:', errorText);
        throw new Error(`Transaction building failed: ${errorText}`);
      }

      const { cborHex } = await response.json();
      console.log('✅ Transaction built successfully');
      console.log('🔍 CBOR length:', cborHex.length);

      // Sign and submit transaction
      console.log('🔐 Requesting wallet signature...');
      const signedTx = await walletApi.signTx(cborHex);

      console.log('📤 Submitting transaction...');
      const txHash = await walletApi.submitTx(signedTx);

      console.log('✅ SUCCESS! Transaction hash:', txHash);

      // Create vault info
      const vaultInfo = {
        contractAddress: VAULT_CONFIG.contractAddress,
        depositAmount: parseFloat(depositAmount),
        maxTradeAmount: parseFloat(maxTradeAmount),
        txHash: txHash,
        network: VAULT_CONFIG.network,
        userAddress: realPaymentAddress,
        createdAt: new Date().toISOString()
      };

      onVaultCreated(vaultInfo);

    } catch (error) {
      console.error('❌ Vault creation failed:', error);
      console.error('❌ Error details:', JSON.stringify(error, null, 2));

      let errorMessage = 'Unknown error occurred';
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      } else if (error && typeof error === 'object') {
        errorMessage = JSON.stringify(error);
      }

      onError(`Vault creation failed: ${errorMessage}`);
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5 text-blue-500" />
            Agent Vault Creation - Testnet
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {USE_TESTNET && (
            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription>
                🧪 <strong>Testnet Mode</strong> - Safe testing with test ADA. No real funds at risk.
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-4">
            <div>
              <Label htmlFor="deposit">Initial Deposit (ADA)</Label>
              <Input
                id="deposit"
                type="number"
                value={depositAmount}
                onChange={(e) => setDepositAmount(e.target.value)}
                min="1"
                max="10000"
                step="1"
              />
            </div>

            <div>
              <Label htmlFor="maxTrade">Max Trade Amount (ADA)</Label>
              <Input
                id="maxTrade"
                type="number"
                value={maxTradeAmount}
                onChange={(e) => setMaxTradeAmount(e.target.value)}
                min="1"
                max="1000"
                step="1"
              />
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg space-y-2">
            <h4 className="font-medium">Vault Configuration</h4>
            <div className="text-sm text-gray-600 space-y-1">
              <div>📍 Contract: {VAULT_CONFIG.contractAddress.substring(0, 30)}...</div>
              <div>🌐 Network: {VAULT_CONFIG.network}</div>
              <div>👤 Your Wallet: {connectedWallet.address.substring(0, 30)}...</div>
              <div>💰 Your Balance: {connectedWallet.balance.toLocaleString()} {USE_TESTNET ? 'tADA' : 'ADA'}</div>
            </div>
          </div>

          <Button 
            onClick={handleCreateVault}
            disabled={isCreating || !depositAmount || !maxTradeAmount}
            className="w-full"
            size="lg"
          >
            {isCreating ? (
              <>Creating Agent Vault...</>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Create Agent Vault ({depositAmount} {USE_TESTNET ? 'tADA' : 'ADA'})
              </>
            )}
          </Button>

          {isCreating && (
            <Alert>
              <AlertDescription>
                🔄 Creating your Agent Vault... Please approve the transaction in your wallet.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export default AgentVaultCreationSimple;
