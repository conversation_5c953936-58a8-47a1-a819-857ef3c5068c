# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

MRSTRIKE is a comprehensive Mastra-based AI agent system for cryptocurrency trading and analysis, featuring multiple specialized agents for different trading strategies and market analysis. The system integrates with Cardano blockchain, Strike Finance, and various trading platforms.

## Key Components

### 1. Sydney Agents (`sydney-agents/`)
The main Mastra-based agent system containing:
- **Agents**: Specialized AI agents for trading strategies and analysis (`src/mastra/agents/`)
- **Services**: Core trading and blockchain interaction services (`src/mastra/services/`)
- **Tools**: Custom tools for data analysis and execution (`src/mastra/tools/`)
- **Workflows**: Automated trading and analysis workflows (`src/mastra/workflows/`)
- **MCP Integration**: Sone MCP server for advanced assistant capabilities (`src/mastra/mcp/`)

### 2. MISTER Frontend (`sydney-agents/mister-frontend/`)
Next.js 15 application with React 19 using:
- **App Router**: Modern Next.js routing in `src/app/`
- **API Routes**: Backend endpoints in `src/app/api/` for trading operations
- **Components**: Reusable UI components using shadcn/ui (`src/components/`)
- **Supabase Integration**: User authentication and preferences storage
- **Cardano CSL**: Browser-based Cardano transaction signing

### 3. MMISTERMMCP (`MMISTERMMCP/`)
Legacy MCP (Model Context Protocol) server implementation with:
- Trading bot functionality
- Performance tracking  
- User settings management

## Common Development Commands

### Sydney Agents (Main System)
```bash
cd sydney-agents

# Development
npm run dev          # Start Mastra development server
npm run build        # Build the project
npm run start        # Start production server

# Agent Testing
npm run mister       # Start main MISTER agent server
npm run mister:simple # Start simplified MISTER server
npm run mister:demo  # Run demo mode
npm run mister:bridge # Start bridge server
```

### Frontend Development
```bash
cd sydney-agents/mister-frontend

# Development
npm run dev          # Start Next.js development server (with Turbopack)
npm run build        # Build Next.js application
npm run start        # Start production server
npm run lint         # Run ESLint
```

### Legacy MCP System
```bash
cd MMISTERMMCP

# Development
npm run start        # Start portfolio swap trading
npm run mcp          # Start MCP server
npm run dev          # Start frontend development
```

## Testing Commands

### Agent Testing (Sydney Agents)
```bash
cd sydney-agents

# Core Agent Tests
node misterlabs/tests/test-sone-voice.js           # Test Sone voice capabilities
node misterlabs/tests/test-trading-monitor.js      # Test trading monitoring
node misterlabs/tests/test-enhanced-sone.js        # Test enhanced Sone features
node test-vault-trading-flow.js                    # Test agent vault trading

# Specific Strategy Tests
node test-multi-timeframe.js                       # Test multi-timeframe strategies
tsx test-natural-language-backtesting.ts           # Test backtesting workflows

# Cardano Integration Tests
node test-agent-vault.js                          # Test agent vault functionality
node test-real-transactions.js                    # Test real Cardano transactions
node test-cbor-only.js                            # Test CBOR transaction building
```

### Frontend Testing
Frontend testing is done via built-in test pages accessible in the browser:
- `/test-agent-vault` - Agent vault functionality testing
- `/test-strike` - Strike Finance integration testing
- `/backtest-results` - Backtesting results display
- `/test-wasm` - WebAssembly Cardano CSL testing

## Architecture Overview

### Agent System
The system uses Mastra framework with specialized agents:
- **Strike Agent**: Main trading agent for Strike Finance integration
- **Fibonacci Agent**: Fibonacci-based trading strategies
- **Multi-Timeframe Agent**: Multi-timeframe analysis
- **ADA Custom Algorithm Agent**: Custom Cardano trading algorithms
- **Sone Agent**: Personal assistant with voice capabilities
- **Cash Agent**: Cash management and analysis
- **Backtesting Agent**: Strategy backtesting and validation

### Services Architecture
Core services handle different aspects of trading:
- **Strike Finance API**: Direct Strike Finance integration
- **Cardano Balance Service**: Blockchain balance monitoring
- **Vault Trading Service**: Automated vault trading operations
- **Unified Execution Service**: Centralized trade execution
- **Fee Calculator**: Trading fee analysis and optimization

### Frontend Architecture
Next.js application with:
- **API Routes**: Backend endpoints for trading operations (`src/app/api/`)
- **Components**: Reusable UI components (`src/components/`)
- **Pages**: Application pages and routing (`src/app/`)
- **Services**: Client-side API integration (`src/lib/api/`)

## Key Configuration Files

- `sydney-agents/mastra.config.js`: Mastra framework configuration
- `sydney-agents/src/mastra/index.ts`: Main agent system initialization
- `sydney-agents/mister-frontend/next.config.ts`: Next.js configuration
- `MMISTERMMCP/railway.toml`: Railway deployment configuration

## Development Patterns and Architecture

### Mastra Framework Structure
- **Agents**: Located in `src/mastra/agents/` - Each agent extends Mastra Agent class
- **Tools**: Located in `src/mastra/tools/` - Utility functions for agents to interact with external APIs
- **Services**: Located in `src/mastra/services/` - Business logic and API integrations
- **Workflows**: Located in `src/mastra/workflows/` - Multi-step automated processes

### Key Development Principles
- **Agent Isolation**: Each agent operates independently with its own tools and context
- **Service Layer**: Core business logic separated from agent implementations
- **Tool Pattern**: External integrations wrapped as Mastra tools for agent consumption
- **Memory Management**: SQLite databases for persistent agent memory across sessions

### Configuration Management
- **Environment Variables**: Configured in `mastra.config.js` for deployment
- **Database Storage**: Multiple SQLite files for different agent contexts:
  - `mastra.db` - Main Mastra framework storage
  - `sone-memory.db` - Sone agent persistent memory
  - `cash-memory.db` - Cash agent memory
  - `strike-memory.db` - Strike trading agent memory

## Database and Storage

The system uses multiple database configurations:
- **Mastra Storage**: LibSQL in-memory database for agent state
- **SQLite Files**: Various `.db` files for persistent storage
- **Supabase**: User preferences and authentication (frontend)

## External Integrations

- **Strike Finance**: Primary trading platform integration
- **Cardano Blockchain**: Native Cardano operations via CSL
- **Blockfrost**: Cardano blockchain data provider
- **Google AI**: Gemini models for agent intelligence
- **TradingView**: Chart data and analysis
- **Railway**: Production deployment platform

## Important Development Notes

### Requirements and Dependencies
- **Node.js**: Requires 18+ (sydney-agents) or 20+ (main package.json)
- **Package Managers**: Both npm and pnpm are used throughout the project
- **TypeScript**: Used throughout with ESM modules (`"type": "module"`)
- **Runtime**: Uses `tsx` for TypeScript execution in development

### Critical Development Guidelines
- **Environment Setup**: API keys are included in `.env` files for immediate functionality
- **Database Persistence**: Agent memory persists across sessions via SQLite files
- **Transaction Security**: All Cardano transactions use CSL for secure signing
- **Fee Analysis**: All trading operations include comprehensive fee calculations
- **User Isolation**: Frontend implements user-based data filtering and storage
- **Testing First**: Extensive test files available - always run relevant tests before deployment

### Common Debugging Commands
```bash
# Debug specific systems
node debug-blockfrost.js                    # Debug Blockfrost API issues
node debug-transaction-builder.js           # Debug Cardano transaction building
node debug-strike-api.js                   # Debug Strike Finance API
node debug-wallet-address.js               # Debug wallet address issues
```

## Production Deployment

### Railway Deployment
The system is configured for Railway deployment with:
- **Cloudflare Integration**: Mastra agents deployed to `substantial-scarce-magazin.mastra.cloud`
- **Next.js Frontend**: Separate Railway service for frontend
- **Environment Management**: Production environment variables in `mastra.config.js`
- **Backtesting Service**: Separate Python-based backtesting service (`sydney-agents/backtesting-service/`)

### Deployment Commands
```bash
cd sydney-agents
npm run build                               # Build Mastra agents
npm run start                              # Start production server

cd sydney-agents/mister-frontend
npm run build                              # Build Next.js frontend
npm run start                              # Start production frontend
```